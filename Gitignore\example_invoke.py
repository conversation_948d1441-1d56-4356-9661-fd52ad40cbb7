import sys
from PyQt5.QtWidgets import QApplication
from ReportShowDialogUI import show_csv_report_dialog

if __name__ == "__main__":
    # Create QApplication instance
    app = QApplication(sys.argv)
    
    # File path to your CSV report
    csv_file_path = r"F:\Mitul - AV Development\Customer\REAL\Accuvelocity_exe\Requests\20250503_150952\Report_20250503_151000_PV-WITH-INVENTORY_V1.csv"
    
    # Message to display at the top of the dialog
    message = "Document processing has been completed successfully.\n\nBelow is the report showing the extracted data from your documents."
    
    # Show the CSV report dialog
    show_csv_report_dialog(csv_file_path, message)
    
    # Start the application event loop
    sys.exit(app.exec_())