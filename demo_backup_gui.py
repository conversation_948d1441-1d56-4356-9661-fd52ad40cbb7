#!/usr/bin/env python3
"""
Demo script for AccuVelocity Backup Report GUI
This script demonstrates the backup report functionality with sample data.
"""

import sys
import os
import json
from datetime import datetime, timedelta

# Add the Sourcecode directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Sourcecode'))

def create_sample_backup_data():
    """Create sample backup data for demonstration"""
    try:
        # Get resource directory path
        from CustomHelper import CGeneralHelper
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        
        if not strResourceDirpath or not os.path.exists(strResourceDirpath):
            print("Resource directory not found. Creating sample data in current directory.")
            strResourceDirpath = "."
        
        # Create sample tracking data
        sample_tracking = {}
        base_date = datetime.now()
        
        # Generate sample data for the last 30 days
        for i in range(30):
            date = base_date - timedelta(days=i)
            date_str = date.strftime("%d-%b-%y")
            
            # Add 1-3 random backup times per day
            import random
            num_backups = random.randint(1, 3)
            times = []
            
            for j in range(num_backups):
                hour = random.randint(8, 20)
                minute = random.randint(0, 59)
                time_str = f"{hour:02d}{minute:02d}"
                times.append(time_str)
            
            sample_tracking[date_str] = sorted(times)
        
        # Save sample tracking data
        tracking_file = os.path.join(strResourceDirpath, "BackupFileTracking.json")
        with open(tracking_file, "w") as f:
            json.dump(sample_tracking, f, indent=4)
        
        print(f"Sample backup tracking data created: {tracking_file}")
        return True
        
    except Exception as e:
        print(f"Error creating sample data: {e}")
        return False

def demo_backup_report():
    """Demonstrate the backup report functionality"""
    print("=" * 70)
    print("ACCUVELOCITY BACKUP REPORT GUI DEMONSTRATION")
    print("=" * 70)
    
    try:
        # Create sample data
        print("Step 1: Creating sample backup data...")
        if not create_sample_backup_data():
            print("Failed to create sample data. Proceeding with existing data.")
        
        # Generate backup report
        print("Step 2: Generating backup report...")
        from CustomHelper import CBackupFileManager
        CBackupFileManager.MSCreateBackUpFileReport()
        print("✓ Backup report generated successfully!")
        
        # Show report statistics
        print("\nStep 3: Displaying report statistics...")
        show_report_stats()
        
        # Launch GUI
        print("\nStep 4: Launching Backup Report GUI...")
        print("The GUI will display:")
        print("  • Daily backups with green highlighting")
        print("  • Weekly backups with blue highlighting") 
        print("  • Monthly backups with yellow highlighting")
        print("  • Export to TXT functionality")
        print("  • Refresh data capability")
        
        from BackupReportUI import show_backup_report
        show_backup_report()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please ensure all required modules are available.")
    except Exception as e:
        print(f"Demo error: {e}")

def show_report_stats():
    """Display backup report statistics"""
    try:
        from CustomHelper import CGeneralHelper
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        
        if not strResourceDirpath:
            strResourceDirpath = "."
            
        report_file = os.path.join(strResourceDirpath, "BackupFileReport.json")
        
        if os.path.exists(report_file):
            with open(report_file, "r") as f:
                data = json.load(f)
            
            daily_count = len(data.get("DayWise", {}))
            weekly_count = len(data.get("WeekWise", {}))
            monthly_count = len(data.get("MonthWise", {}))
            
            print(f"  • Daily backup entries: {daily_count}")
            print(f"  • Weekly backup groups: {weekly_count}")
            print(f"  • Monthly backup groups: {monthly_count}")
            
            # Show sample daily data
            if daily_count > 0:
                sample_date = list(data["DayWise"].keys())[0]
                sample_data = data["DayWise"][sample_date]
                print(f"  • Sample: {sample_date} has {sample_data['count']} backup(s)")
        else:
            print("  • No backup report file found")
            
    except Exception as e:
        print(f"  • Error reading report stats: {e}")

def show_usage_examples():
    """Show different ways to use the backup report GUI"""
    print("\n" + "=" * 70)
    print("USAGE EXAMPLES")
    print("=" * 70)
    
    print("\n1. Command Line Usage:")
    print("   python Main.py --show-backup-report-gui")
    
    print("\n2. Programmatic Usage:")
    print("   from CustomHelper import CBackupFileManager")
    print("   CBackupFileManager.MSShowBackupReportGUI()")
    
    print("\n3. Direct GUI Launch:")
    print("   from BackupReportUI import show_backup_report")
    print("   show_backup_report()")
    
    print("\n4. Generate Report Only:")
    print("   python Main.py --generate-backup-report")

if __name__ == "__main__":
    print("AccuVelocity Backup Report GUI Demo")
    print("This demo will create sample data and launch the GUI.")
    
    choice = input("\nProceed with demo? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        demo_backup_report()
        show_usage_examples()
    else:
        print("Demo cancelled.")
        show_usage_examples()
