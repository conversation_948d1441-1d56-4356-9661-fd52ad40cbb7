import json
import os
import sys
import threading
import requests
from CustomLogger import CLogger
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QProgressBar, QLabel, QFrame,
    QApplication, QGraphicsDropShadowEffect, QSizePolicy
)
from PyQt5.QtCore import QTimer, Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap, QPainter, QFontMetrics

from CustomHelper import CGeneralHelper, CResoucedataHelper


def get_centered_pixmap(image_path, fixed_width=700, fixed_height=150):
    """
    Load an image from image_path, scale it while preserving aspect ratio,
    and draw it centered onto a fixed-size QPixmap.
    """
    original = QPixmap(image_path)
    if original.isNull():
        return QPixmap(fixed_width, fixed_height)
    scaled = original.scaled(fixed_width, fixed_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
    final_pixmap = QPixmap(fixed_width, fixed_height)
    final_pixmap.fill(Qt.transparent)
    painter = QPainter(final_pixmap)
    x = (fixed_width - scaled.width()) // 2
    y = (fixed_height - scaled.height()) // 2
    painter.drawPixmap(x, y, scaled)
    painter.end()
    return final_pixmap


class DocumentProcessingWindow(QWidget):
    """PyQt5 window displaying document processing progress with a slideshow and countdown."""
    
    cancel_requested = pyqtSignal()
    processing_complete = pyqtSignal()
    image_ready = pyqtSignal(str)  # Signal to indicate an image is ready
    
    def __init__(self, total_estimated_time, strImage_url, strDownload_Url, strVoucherType, strClientREQID):
        """
        Initialize the DocumentProcessingWindow.
        
        Args:
            total_estimated_time (int): Total estimated time for processing in seconds
            strImage_url (str): URL to get the list of images
            strDownload_Url (str): URL template to download images
            strVoucherType (str): Type of voucher being processed
            strClientREQID (str): Client request ID
        """
        super().__init__()
        self.setWindowFlags(self.windowFlags())
        self.total_estimated_time = total_estimated_time
        self.remaining_time = self.total_estimated_time
        self.Image_Url = strImage_url
        self.download_url_template = strDownload_Url
        self.strVoucherType = strVoucherType
        self.strClientREQID = strClientREQID
        
        self.countdown_timer = QTimer(self)
        self.countdown_timer.timeout.connect(self.tickCountdown)

        # Get the resource directory path
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        # Path to store downloaded images locally
        self.local_image_dir = os.path.join(strResourceDirpath, r"Advertisement")
        if not os.path.exists(self.local_image_dir):
            os.makedirs(self.local_image_dir)
            CLogger.MCWriteLog("info", f"Created local image directory at {self.local_image_dir}")

        self.image_files = []  # List to hold paths of downloaded images
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
        self.image_files = [
            os.path.join(self.local_image_dir, f)
            for f in os.listdir(self.local_image_dir)
            if os.path.splitext(f)[1].lower() in image_extensions
        ]
        self.current_image_index = 0
        
        # Connect image_ready signal to slot
        self.image_ready.connect(self.on_image_ready)
        
        # Start image download thread
        self.download_thread = threading.Thread(target=self.sync_images_from_server)
        self.download_thread.start()

        self.status_text = "Waiting to Start"
        
        # Initialize UI
        self.initUI()

    def initUI(self):
        # Set window properties
        self.setWindowTitle(f"{self.strVoucherType} Processing Started - {self.strClientREQID}")
        self.setMinimumSize(750, 350)  # Minimum size to fit 700x150 image + other elements
        self.setStyleSheet("background-color: #2C3E50;")  # Dark background for contrast

        # Main layout with margins and spacing
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        self.setLayout(main_layout)

        # Section 1: Image (Ads) with shadow effect
        image_container = QFrame()
        image_container.setStyleSheet("background-color: transparent;")
        image_layout = QVBoxLayout(image_container)
        image_layout.setContentsMargins(50, 0, 50, 50)

        self.image_label = QLabel(self)
        self.image_label.setFixedSize(750, 150)  # Fixed size for 700x150 images
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("border: none;")
        
        image_layout.addWidget(self.image_label)

        # Add shadow for a 3D effect
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(30)
        shadow.setOffset(5, 5)
        shadow.setColor(Qt.black)
        image_container.setGraphicsEffect(shadow)

        main_layout.addWidget(image_container, alignment=Qt.AlignCenter)

        # Load initial image using the centered pixmap helper
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        default_image_path = os.path.join(strResourceDirpath, "Default_Ad.png")
        if self.image_files:
            pixmap = get_centered_pixmap(self.image_files[0])
        else:
            pixmap = get_centered_pixmap(default_image_path)
            CLogger.MCWriteLog("info", "No images found in local directory, using default image.")
        self.image_label.setPixmap(pixmap)

        # Section 2: Header with icon and text
        header_layout = QHBoxLayout()
        icon_label = QLabel()
        strIconPath = os.path.join(strResourceDirpath, "AvLogo.png")
        icon_label.setPixmap(QIcon(strIconPath).pixmap(48, 48))
        icon_label.setFixedSize(60, 60)
        header_layout.addWidget(icon_label)

        text_layout = QVBoxLayout()
        title = QLabel(f"{self.strVoucherType} Processing Started")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title.setStyleSheet("color: #ECF0F1;")
        self.subtitle = QLabel("Please wait while your files are being processed. A report will appear once processing is complete.")
        self.subtitle.setFont(QFont("Segoe UI", 10))
        self.subtitle.setStyleSheet("color: #BDC3C7;")
        self.subtitle.setWordWrap(True)
        self.subtitle.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        
        # Set a fixed height for the subtitle to prevent layout shifts
        font = QFont("Segoe UI", 10)
        fm = QFontMetrics(font)
        line_height = fm.lineSpacing()
        num_lines = 3  # Assuming up to 3 lines for the longest text
        self.subtitle.setFixedHeight(line_height * num_lines)
        
        text_layout.addWidget(title)
        text_layout.addWidget(self.subtitle)
        header_layout.addLayout(text_layout)
        main_layout.addLayout(header_layout)

        # Section 3: Progress Area
        progress_frame = QFrame()
        progress_frame.setStyleSheet("background-color: #34495E; border-radius: 15px;")
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setContentsMargins(5, 5, 5, 5)
        progress_layout.setSpacing(5)

        self.status_label = QLabel("Waiting to Start <br><b>Estimated Time Remaining: --</b>")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Segoe UI", 10))
        self.status_label.setStyleSheet("color: white;")
        self.status_label.setWordWrap(True)
        self.status_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        progress_layout.addWidget(self.status_label)

        self.progress_bar = QProgressBar(self)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setFixedHeight(20)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #3498DB;
                border-radius: 10px;
                text-align: center;
                color: white;
                background-color: #34495E;
            }
            QProgressBar::chunk {
                background-color: #1ABC9C;
                border-radius: 10px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)

        main_layout.addWidget(progress_frame)

        # Footer (optional branding)
        footer_label = QLabel("Powered by AccuVelocity AI")
        footer_label.setAlignment(Qt.AlignCenter)
        footer_label.setFont(QFont("Segoe UI", 8))
        footer_label.setStyleSheet("color: #BDC3C7;")
        main_layout.addWidget(footer_label)

        # Image timer for cycling images every 10 seconds
        self.image_timer = QTimer(self)
        self.image_timer.timeout.connect(self.change_image)
        self.image_timer.start(10000)
        strIconPath = os.path.join(strResourceDirpath, "AvLogo.ico")
        self.setWindowIcon(QIcon(strIconPath))
        
    def sync_images_from_server(self):
        """Sync images from the server and handle errors gracefully."""
        try:
            CLogger.MCWriteLog("info", "Starting to sync images from server")
            response = requests.get(self.Image_Url)
            if response.status_code == 200:
                server_images = response.json().get("image_files", [])
                CLogger.MCWriteLog("info", f"Received {len(server_images)} images from server.")
                for image_name in server_images:
                    local_image_path = os.path.join(self.local_image_dir, image_name)
                    if not os.path.exists(local_image_path):
                        try:
                            # Replace {image_name} in URL template
                            actual_download_url = self.download_url_template.replace("{image_name}", image_name)
                            # Download image
                            image_data = requests.get(actual_download_url).content
                            with open(local_image_path, 'wb') as f:
                                f.write(image_data)
                            self.image_files.append(local_image_path)
                            CLogger.MCWriteLog("info", f"Downloaded image: {image_name}")
                            self.image_ready.emit(local_image_path)
                        except Exception as e:
                            CLogger.MCWriteLog("error", f"Error downloading image {image_name}: {e}")
            else:
                CLogger.MCWriteLog("warning", "Failed to fetch images from server.")
        except Exception as e:
            CLogger.MCWriteLog("error", f"Error syncing images from server: {e}")

    def on_image_ready(self, image_path):
        """Handle the image_ready signal by setting the first downloaded image immediately."""
        if len(self.image_files) == 1:
            pixmap = get_centered_pixmap(image_path)
            self.image_label.setPixmap(pixmap)
            CLogger.MCWriteLog("info", "Set first downloaded image immediately.")

    def startCountdownTimer(self):
        """Start the countdown timer."""
        CLogger.MCWriteLog("info", "Starting countdown timer.")
        self.countdown_timer.start(1000)

    def tickCountdown(self):
        """Update the countdown timer and status label."""
        if self.remaining_time > 0:
            self.remaining_time -= 1
            minutes = self.remaining_time // 60
            seconds = self.remaining_time % 60
            self.status_label.setText(f"{self.status_text} <br><b>Estimated Time Remaining: {minutes} min {seconds} sec</b>")
        else:
            self.countdown_timer.stop()
            self.status_label.setText(f"{self.status_text} <br><b>Estimated Time Remaining: 0 min 0 sec</b>")
            CLogger.MCWriteLog("info", "Countdown finished.")

    def setProgressAndStatus(self, progress_percent, status_text, remaining_time=None):
        """Update progress bar and status text."""
        self.progress_bar.setValue(progress_percent)
        self.status_text = status_text
        if remaining_time is not None:
            self.remaining_time = remaining_time
        minutes = self.remaining_time // 60
        seconds = self.remaining_time % 60
        self.status_label.setText(f"{self.status_text} <br><b>Estimated Time Remaining: {minutes} min {seconds} sec</b>")
        CLogger.MCWriteLog("info", f"Progress: {progress_percent}% - {status_text}")

        if progress_percent >= 100:
            self.status_label.setText("Processing Complete")
            if hasattr(self, 'image_timer'):
                self.image_timer.stop()
            QTimer.singleShot(5000, self.close)  # Close after 5 seconds

    def change_image(self):
        """Change the current image in the slideshow without animation."""
        if self.image_files:
            self.current_image_index = (self.current_image_index + 1) % len(self.image_files)
            pixmap = get_centered_pixmap(self.image_files[self.current_image_index])
            self.image_label.setPixmap(pixmap)

    def setSubtitleText(self, text):
        """Update the subtitle text."""
        self.subtitle.setText(text)
        CLogger.MCWriteLog("info", f"Subtitle text updated: {text}")


if __name__ == '__main__':
    CLogger.MCSetupLogging()

    app = QApplication(sys.argv)
    total_estimated_time = 100
    Imageurl = "http://192.168.1.18:8029/ad/get_images"
    Downloadurl = "http://192.168.1.18:8029/ad/download_image/{image_name}"
    window = DocumentProcessingWindow(total_estimated_time, Imageurl, Downloadurl, "Purchase With Inventory", "REQ_CSAVDEVELOPER_TS124346_UID6DNBKY")
    window.show()

    progress = 0
    current = 1
    total = 5

    def update_progress():
        global progress
        if progress <= 100:
            remaining_time = total_estimated_time - (progress * total_estimated_time // 100)
            window.setProgressAndStatus(
                progress,
                f"Step {current}/{total}: Your Tally stock items have been successfully exported and Documents are now ready for seamless AI-powered processing.",
                remaining_time
            )
            progress += 1
        else:
            timer.stop()

    timer = QTimer()
    timer.timeout.connect(update_progress)
    timer.start(100)

    window.startCountdownTimer()
    sys.exit(app.exec_())