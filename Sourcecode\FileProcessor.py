import math
import sys
import os
import time
import fitz

from ProgressbarUI import  DocumentProcessingWindow
sys.path.append(".")
sys.path.append(os.path.abspath(os.path.dirname(__file__)))
# import requests
import json
import traceback
import threading
import uuid
from PopupWindow import CPopupWindow
from CustomLogger import CLogger
from CustomHelper import CArgumentParser, CDocument, CGeneralHelper, CTallyExportHelper, CLicenseHelper, CResoucedataHelper
from CustomException import InvalidFilePathError, NoFilesProvidedError, UnsupportedFileFormatError
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal
import httpx
from myFileHelper import CFileHelper
import os
from datetime import datetime
import zipfile
from PyQt5.QtCore import QMetaObject, Qt, Q_ARG,QThread,pyqtSignal
from ReportShowDialogUI import show_csv_report_dialog
from EncryptResourceConfig import CReadDeveloperConfig
import socket

class SignalHandler(QObject):
    showPopupSignal = pyqtSignal(str, str)
    
class WorkerThread(QThread):
    progress_update = pyqtSignal(int,str)

    def __init__(self, total_time):
        super().__init__()
        self.total_time = total_time
        self._is_running = True  # Flag to control thread execution



class ReportSignalHandler(QObject):
    showReportSignal = pyqtSignal(str, str)

global_signal_handler = SignalHandler()
global_Reportsignal_handler = ReportSignalHandler()

class CFileProcessor:
    
    def __init__(self, strCommand, strUserToken, bTestMode, strVoucherType="PV_WITH_INVENTORY", bEnableStockItemExport=False, bIsMultipleVendorSplitAlgo=False,strClientREQID="", objClientReqGeneratedAt = None):
        """
        Initializes the File Processor with the provided file paths.
        """
        self.strUserToken = strUserToken
        self.strClientREQID=strClientREQID
        self.signal_handler = global_signal_handler
        self.csv_report_signal = global_Reportsignal_handler

        self.signal_handler.showPopupSignal.connect(self.show_popup)
        self.csv_report_signal.showReportSignal.connect(show_csv_report_dialog)

        self.bTestMode = bTestMode
        self.strVoucherType = strVoucherType
        self.strExePath = CFileHelper._get_executable_directory()
        self.dictUserConfig = CResoucedataHelper.MSGetUserConfig()
        self.dictDeveloperConfig = CResoucedataHelper.MSGetDeveloperConfig()
        self.strUploadedDocPath = self.dictUserConfig.get("ExportRequest", {}).get("RequestDir", os.path.join(self.strExePath, "Requests"))
        self.bEnableStockItemExport = bEnableStockItemExport
        self.bIsMultipleVendorSplitAlgo = bIsMultipleVendorSplitAlgo
        # UserConfig and DeveloperConfig Get
        
        self.dictAPIEndPoints = self.dictDeveloperConfig.get("ExportRequest", {}).get("APIEndPoints", {})
        self.SinglePageLimit = self.dictDeveloperConfig.get("PageLimitdetails",{}).get("SingleDocumentLimit",25)
        self.OverAllPageLimit = self.dictDeveloperConfig.get("PageLimitdetails",{}).get("OverallDocumentLimit",50)
        # Extract configuration values
        self.strTallyServerUrl = self.dictUserConfig.get("ExportRequest", {}).get("ReqUrl")
        self.strVersionInfoFile = self.dictUserConfig.get("ExportRequest",{}).get("VersionInfo")
        self.strVersionInfoFile = os.path.join(self.strExePath, self.strVersionInfoFile)
        self.iPageAllowedPerDay = self.dictUserConfig.get("ExportRequest",{}).get("APILIMITS").get("iPagesAllowedPerDay")
        self.iRequestAllowedPerDay = self.dictUserConfig.get("ExportRequest",{}).get("APILIMITS").get("iRequestAllowedPerDay")
        # EXE Version Details , User Unique UUID Mac Address
        self.dictInstalledExeDetail = CResoucedataHelper.MSGetAccuvelocityConfig()
        current_mac = hex(uuid.getnode()).replace("0x", "").upper()
        self.dictInstalledExeDetail["USER_UUID"] = str(current_mac)
        self.dictInstalledExeDetail["iPageAllowedPerDay"] = self.iPageAllowedPerDay
        self.dictInstalledExeDetail["iRequestAllowedPerDay"] = self.iRequestAllowedPerDay
        self.dictInstalledExeDetail["strClientREQID"]=strClientREQID
        self.dictInstalledExeDetail["ClientReqGeneratedAt"] = objClientReqGeneratedAt
        self.dictInstalledExeDetail["MultipleVendorEnable"] = bIsMultipleVendorSplitAlgo
        self.dictInstalledExeDetail["StockItemConnectEnable"] = bEnableStockItemExport
        self.dictInstalledExeDetail["ClientImportedXMLType"] = "EXE_Initiated_Request"
        # Add Flag For Single Page invoice here
        self.dictInstalledExeDetail["bSmartVendorDetectAlgo"] = self.dictUserConfig.get("ExportRequest", {}).get('bSmartVendorDetectAlgo',False)
        self.dictInstalledExeDetail["iSplitPages"] = self.dictUserConfig.get("ExportRequest", {}).get('iSplitPages',1)


        self.lsReqDocMetaData = None
        CLogger.MCWriteLog("info", f"Starting file processing for command: {strCommand}")
        try:
            self.valid_file_paths = CArgumentParser.MSGetValidFilePathFromArgument(strCommand)
            
        except InvalidFilePathError as Ie:
            CLogger.MCWriteLog("error", f"Error initializing file processor: {Ie}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            self.signal_handler.showPopupSignal.emit("error", f"{Ie}")
            sys.exit(0)
        except  NoFilesProvidedError as e:
            CLogger.MCWriteLog("error", f"Error initializing file processor: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            # self.signal_handler.showPopupSignal.emit("error", str(e))      # ! Commented for using export function, else will show popup of no files provided
            print(e)
            sys.exit(0)
        
        except ValueError as ve:
            CLogger.MCWriteLog("error", f"Error initializing file processor: {ve}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            self.signal_handler.showPopupSignal.emit("error", f"{ve}")
            sys.exit(0)
        
        except Exception as e:
            CLogger.MCWriteLog("error", f"Error initializing file processor: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            self.signal_handler.showPopupSignal.emit("error", "Please provide valid file paths separated by semicolons")
            sys.exit(0)


    def create_date_time_folder(self):
        
        try:
            
            # Get current date and time
            current_date_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Create the path for the new folder
            self.strDateTimeFolderPath = os.path.join(self.strUploadedDocPath, current_date_time)
            self.strTallyResponseXMLDir =  os.path.join(self.strUploadedDocPath, current_date_time, "TallyImportedResponseXML")
            # Ensure the directory exists
            os.makedirs(self.strTallyResponseXMLDir, exist_ok=True)  
            # Create the directory if it doesn't exist
            os.makedirs(self.strDateTimeFolderPath, exist_ok=True)
            CLogger.MCWriteLog("info", f"Folder created: {self.strDateTimeFolderPath}")

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to create document upload folder at location {self.strDateTimeFolderPath}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")


    def show_popup(self, message_type, message):
        CPopupWindow(message=message, message_type=message_type)

    def ExportInventoryTallyData(func):
        """
        Decorator to execute specific code before invoking the decorated method.
        """
        def wrapper(self, *args, **kwargs):
            # Code to be executed before the main method
            strExportType = "ExportFilteredStockItem"
            CTallyExportHelper.MSExportNSaveTallyData(strExportType, self.strUserToken,lsalternative_bases=self.lsalternative_bases)
            
            # Proceed with the actual method
            return func(self, *args, **kwargs)
        
        return wrapper
    
    # @ExportInventoryTallyData
    def process_documents(self):
        """
        Processes each file by creating a CDocument object
        and calling the process_document API.
        """
        try:
            lsObjDocuments = []
            for file_path in self.valid_file_paths:
                try:
                    objDocument = CDocument(file_path)
                    lsObjDocuments.append(objDocument)
                except (UnsupportedFileFormatError, Exception) as e:
                    file_extension = os.path.splitext(file_path)[-1]  # Extract the file extension
                    user_friendly_message = (
                        f"Unsupported file format detected!\n\n"
                        f"File: {file_path}\n"
                        f"Extension: {file_extension}\n\n"
                        f"Please ensure the file is in a supported format and try again."
                    )
                    
                    # Log the error details for debugging
                    CLogger.MCWriteLog("error", f"Error processing file {file_path}: {e}")
                    CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
                    
                    # Show the user-friendly popup
                    self.signal_handler.showPopupSignal.emit("error", user_friendly_message)
                    return

            self.create_date_time_folder()
            self.save_documents(lsObjDocuments)
            self.workers=self.dictInstalledExeDetail.get("worker",1)
            self.lsalternative_bases=self.dictInstalledExeDetail.get("apiEndpoints",[])

            self.total_estimated_time =self.estimated_processing_time(self.lsReqDocMetaData,self.workers)
            self.intCurrentStep = 1
            self.intTotalSteps =1

            
            
            if self.bEnableStockItemExport:
                self.total_estimated_time+=3
                self.intTotalSteps=5
                
            else:
                self.total_estimated_time=self.total_estimated_time
                self.intTotalSteps=4
                
                
                
            self.progress_window=None
        
            strImage_url=self.dictAPIEndPoints.get("Imageurl", "")
            try:
                strImage_url=str(CArgumentParser.MSGet_working_endpoint(strImage_url,self.lsalternative_bases))
                CLogger.MCWriteLog("info",f"Working URL selected: {strImage_url}")
                
            except Exception as e:
                CLogger.MCWriteLog("error",f"Error: {str(e)}")
            
            
            strDownload_Url=self.dictAPIEndPoints.get("Downloadurl", "")
            try:
                strDownload_Url=str(CArgumentParser.MSGet_working_endpoint(strDownload_Url,self.lsalternative_bases))
                CLogger.MCWriteLog("info",f"Working URL selected: {strDownload_Url}")
            except Exception as e:
                CLogger.MCWriteLog("error",f"Error: {str(e)}")
             
            if self.bIsMultipleVendorSplitAlgo:
                self.total_estimated_time=self.total_estimated_time*2  # 2 times extra time
                
            self.progress_window = DocumentProcessingWindow(self.total_estimated_time*60,strImage_url,strDownload_Url,self.strVoucherType,self.strClientREQID)
            self.progress_window.show()
            self.progress_window.startCountdownTimer()
            
            

            self.worker_thread = WorkerThread(self.total_estimated_time * 60)
            self.worker_thread.progress_update.connect(self.progress_window.setProgressAndStatus)
            
            self.worker_thread.start()


            # self.worker_thread.progress_update.emit(10,"Your document has been successfully received and is ready for AI-assisted processing.")
            
            

            
            

            
            # Code to be executed before the main method
            if self.bEnableStockItemExport:
                self.worker_thread.progress_update.emit(5, f"Step {self.intCurrentStep}/{ self.intTotalSteps}:Your Tally stock items are being exported to ensure we're working with the latest stock details.")
                self.intCurrentStep+=1
                
                strExportType = "ExportFilteredStockItem"
                CTallyExportHelper.MSExportNSaveTallyData(strExportType, self.strUserToken,lsalternative_bases=self.lsalternative_bases)
                self.worker_thread.progress_update.emit(10,f"Step {self.intCurrentStep}/{self.intTotalSteps}: Your Tally stock items have been successfully exported. Preparing your document for intelligent processing.")
                self.intCurrentStep+=1
            else: 
                self.worker_thread.progress_update.emit(10,f"Step {self.intCurrentStep}/{self.intTotalSteps}: Your document has been successfully received and is being prepared for intelligent processing."
)
                self.intCurrentStep+=1
            
            # self.worker_thread.progress_update.emit(10,"Your document has been successfully received and is ready for AI-assisted processing.")
            CLogger.MCWriteLog("info", f"File processing started for files: {self.valid_file_paths}")
            
            
            processing_thread = threading.Thread(
                target=self.process_document_api, 
                args=(lsObjDocuments, self.strUserToken)
            )
            processing_thread.start()
            self.progress_window.setSubtitleText(f"File processing has started. Below are the files to be processed:\n"
                    + "\n".join([f"{index+1}. {file_path}" for index, file_path in enumerate(self.valid_file_paths)])
                    + "\nOnce processing is complete, a report window will appear. In the meantime, you can continue using the application.")
            return
        except Exception as e:
            if str(e).startswith("ValidationError"):
                CLogger.MCWriteLog("error", f"Error processing documents: {e}")
                CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
                self.signal_handler.showPopupSignal.emit("error", str(e).replace('ValidationError: ',""))
                # self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")
                self.progress_window.processing_complete.connect(QApplication.quit)
                return
            CLogger.MCWriteLog("error", f"Error processing documents: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            self.signal_handler.showPopupSignal.emit("error", "We couldn't process the provided documents. Please manually enter the documents into Tally.")
            self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")
            self.progress_window.processing_complete.connect(QApplication.quit)
            return
    
    def estimated_processing_time(self, list_of_files, workers, processing_time_per_file=1.4):
            try:
                # Calculate the total number of files
                total_files = len(list_of_files)
                
                # Calculate the total processing time assuming 1 minute per file
                total_processing_time = total_files * processing_time_per_file
                
                # Calculate the estimated time with workers (ceil value)
                estimated_time = math.ceil(total_processing_time / workers)

                # Return the estimated time
                return estimated_time

            except Exception as e:
                CLogger.MCWriteLog("error", f"Error calculating estimated processing time: {e}")
                CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
                return None




    def save_documents(self, lsObjDocuments):
        try:
            file_paths = []  # List to store file paths of saved documents
            list_of_files = []  # List to store file metadata (filename, type, location)
            self.dictInstalledExeDetail["ClientReqDir"] = self.strDateTimeFolderPath
            for doc in lsObjDocuments:
                strclientDocRecivelocation = os.path.join(self.strDateTimeFolderPath, doc.filename)

                # just save the document
                with open(strclientDocRecivelocation, "wb") as f:
                    f.write(doc.read_file())

                filename_ext = os.path.splitext(strclientDocRecivelocation)[1].lower()

                # Page Count initialization
                iSinglePDFPages = 0 
                iOverallPDFPages = 0
                
                # Check if the document is a ZIP file
                if filename_ext == '.zip' and zipfile.is_zipfile(strclientDocRecivelocation):
                    # Define a path for the extracted files
                    extract_dir = self.strDateTimeFolderPath

                    # Extract the ZIP file and get the list of file paths
                    extracted_files = CGeneralHelper.MSExtractZipFile(zip_file_path=strclientDocRecivelocation, extract_dir_path=extract_dir)

                    

                    # Process each extracted file and save it just like non-ZIP files
                    for file in extracted_files:
                        try:
                            filename = os.path.basename(file)
                            file_type = os.path.splitext(filename)[1].lower()  # File type/extension
                            # Prepare the dictionary entry for the extracted file
                            strExtractedFilePath = os.path.join(extract_dir, filename)

                            # Add the extracted file metadata to the list
                            file_metadata = {
                                "filename": filename,
                                "Type": file_type,
                                "location": strExtractedFilePath,
                                "checksum": CDocument.calculate_checksum_from_file(strExtractedFilePath)
                            }
                            # Check if the file is a PDF and count pages if it is
                            if file_type == ".pdf":
                                try:
                                    with fitz.open(strExtractedFilePath) as pdf_document:
                                        file_metadata["page_count"] = pdf_document.page_count
                                        iOverallPDFPages += pdf_document.page_count
                                        iSinglePDFPages = pdf_document.page_count
                                    if iSinglePDFPages > self.SinglePageLimit:
                                        raise ValueError(f"ValidationError: The PDF file you uploaded exceeds AccuVelocity’s limit of {self.SinglePageLimit} pages per file and {self.OverAllPageLimit} pages per request.For assistance with larger files, please contact our Support <NAME_EMAIL> or call us at +91 98989 42935. We're here to help!")

                                except Exception as pdf_error:
                                    if str(pdf_error).startswith('ValidationError'):
                                        raise pdf_error
                                    CLogger.MCWriteLog("error", f"Failed to count pages for PDF {filename}: {pdf_error}")
                            list_of_files.append(file_metadata)
                        
                        except Exception as e:
                            if str(e).startswith('ValidationError'):
                                    raise e
                            CLogger.MCWriteLog("error", f"Documents saved failed for this : {file}.")
                    if iOverallPDFPages > self.OverAllPageLimit:
                        raise ValueError(f"ValidationError: The PDF file you uploaded exceeds AccuVelocity’s limit of {self.SinglePageLimit} pages per file and {self.OverAllPageLimit} pages per request.For assistance with larger files, please contact our Support <NAME_EMAIL> or call us at +91 98989 42935. We're here to help!")

                    # Add extracted files to file_paths for further processing if needed
                    file_paths.extend(extracted_files)
                else:
                    # For non-ZIP files, just save the document
                    file_metadata = {
                        "filename": doc.filename,
                        "Type": os.path.splitext(doc.filename)[1].lower(),  # File type/extension
                        "location": strclientDocRecivelocation,
                        "checksum":doc.checksum
                    }

                    file_type = os.path.splitext(doc.filename)[1].lower()

                    # Check if the file is a PDF and count pages if it is
                    if file_type == ".pdf":
                        try:
                            with fitz.open(doc.file_path) as pdf_document:
                                file_metadata["page_count"] = pdf_document.page_count
                                iSinglePDFPages += pdf_document.page_count
                        except Exception as pdf_error:
                            CLogger.MCWriteLog("error", f"Failed to count pages for PDF {filename}: {pdf_error}")
                    list_of_files.append(file_metadata)
                    if iSinglePDFPages > self.SinglePageLimit:
                        raise ValueError(f"ValidationError: The PDF file you uploaded exceeds AccuVelocity’s limit of {self.SinglePageLimit} pages per file and {self.OverAllPageLimit} pages per request.For assistance with larger files, please contact our Support <NAME_EMAIL> or call us at +91 98989 42935. We're here to help!")

                CLogger.MCWriteLog("info", "Documents saved successfully.")
                
            
            
            self.lsReqDocMetaData = list_of_files
            
            # Return the list of dictionaries containing file metadata

            return list_of_files

        except Exception as e:
            if str(e).startswith('ValidationError'):
                raise e
            CLogger.MCWriteLog("error", f"Error saving documents: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")



    def MSetClientRequestDeliveryAt(self):
        """
        Calls the delivery at API endpoint to update client request delivery status.
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.strUserToken}"
            }
            strClientDetail = json.dumps(self.dictInstalledExeDetail)  # Serialize to JSON string 
            data = {}
            
            # Will wait for 10 minutes for server response
            with httpx.Client(timeout=600) as client:
                response = client.post(
                    self.dictAPIEndPoints['DeliveryAtURL'],
                    headers=headers,
                    params={"strClientDetail": strClientDetail},
                    data=data
                )

            if response.status_code == 200:
                data = response.json()
                CLogger.MCWriteLog("info", f"Client request delivery API call completed successfully with response: {response.json()}.")
                return data
            else:
                CLogger.MCWriteLog("error", f"Client request delivery API call failed with status code {response.status_code} and response: {response.text}.")
                return None

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to update client request delivery status: {str(e)}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            return None


    def process_document_api(self, lsObjDocuments, strToken):
        """
        Calls the process document API.
        """
        try:
            headers = {
                "Authorization": f"Bearer {strToken}",
            }
            files = []
            checksums = []
            
            strInstallExeDetail = json.dumps(self.dictInstalledExeDetail)  # Serialize to JSON string 
            CLogger.MCWriteLog("info", f"Version Information: {strInstallExeDetail}")

            for doc in lsObjDocuments:
                file_content = doc.read_file()
                files.append(("documents", (doc.filename, file_content, doc.content_type)))
                checksums.append(doc.checksum)

            data = {
                "checksums": ",".join(checksums)
            }
            


            # Create the folder to save the response zip file
            os.makedirs(self.strDateTimeFolderPath, exist_ok=True)
            zip_file_path = os.path.join(self.strDateTimeFolderPath, "processed_document.zip")

            # Will wait for 10 minutes for server response
            strDocProcessingURL = self.dictAPIEndPoints.get("DocProcessing", "")
            
            try:
                strDocProcessingURL=str(CArgumentParser.MSGet_working_endpoint(strDocProcessingURL,self.lsalternative_bases))
                CLogger.MCWriteLog("info",f"Working URL selected: {strDocProcessingURL}")
            except Exception as e:
                CLogger.MCWriteLog("error",f"Error: {str(e)}")
            

            with httpx.Client(timeout=1200) as client:
                response = client.post(
                    strDocProcessingURL,
                    headers=headers,
                    params={"bTestMode": self.bTestMode, "strVoucherType":self.strVoucherType,"strSerializeUserConfig": strInstallExeDetail,"lsClientDocMetaData":self.lsReqDocMetaData, "bIsMultivendorDoc":self.bIsMultipleVendorSplitAlgo,"strSystemName":os.getlogin()},
                    files=files,
                    data=data
                )

            if response.status_code != 200:
                CLogger.MCWriteLog("warning", f"API error: {response.status_code}, Response: {response.json()}")

                # Extract error message if available in the response
                error_detail = response.json().get('detail', 'Unexpected error occurred.')
                
                # User-friendly error message
                user_friendly_msg = (
                    f"WARNING:  while processing your request. "
                    f"Details: {error_detail}"
                )
                # Invoke signal to show popup with user-friendly message
                QMetaObject.invokeMethod(
                    self.signal_handler,
                    "showPopupSignal",
                    Qt.QueuedConnection,
                    Q_ARG(str, "error"),
                    Q_ARG(str, user_friendly_msg)
                )
                self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")
                self.dictInstalledExeDetail["CReqDeliveredTimeAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()
                self.MSetClientRequestDeliveryAt()
                return

            # Get checksum from headers
            response_checksum = response.headers.get("X-Zip-Checksum")
            if not response_checksum:
                self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")
                self.dictInstalledExeDetail["CReqDeliveredTimeAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()
                self.MSetClientRequestDeliveryAt()
                raise ValueError("Checksum not received in response headers.")

            # Save the streamed zip file
            with open(zip_file_path, "wb") as f:
                for chunk in response.iter_bytes():
                    f.write(chunk)

            # Verify checksum of the saved zip file
            saved_file_checksum = CDocument.MSCalculateChecksum(zip_file_path)
            if saved_file_checksum != response_checksum:
                self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")
                self.dictInstalledExeDetail["CReqDeliveredTimeAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()
                self.MSetClientRequestDeliveryAt()
                raise ValueError("Checksum mismatch! The downloaded file is corrupted.")

            # Unzip  Zip File 
            lsUnzipFilePaths = CGeneralHelper.MSExtractZipFile(zip_file_path=zip_file_path, extract_dir_path=self.strDateTimeFolderPath)
            self.worker_thread.progress_update.emit(70,f"Step {self.intCurrentStep}/{ self.intTotalSteps}:The document is being efficiently integrated into the Tally system through AI-powered automation.")
            self.intCurrentStep+=1
            
            
            
            # Import received XML files and send the corresponding Tally response XML zip file to the AHM server.
            self.MSProcessXMLFiles(lsUnzipFilePaths, strToken=strToken,lsalternative_bases=self.lsalternative_bases)
            
            extracted_files = []
            # Unzip the file and get the list of extracted file paths
            with zipfile.ZipFile(zip_file_path, "r") as zip_ref:
                zip_ref.extractall(self.strDateTimeFolderPath)
                extracted_files = zip_ref.namelist()
                
            extracted_file_paths = [
                os.path.join(self.strDateTimeFolderPath, file_name)
                for file_name in extracted_files
            ]

            # Find the file starting with "Report_"
            strReportFilePath = next(
                (
                    file_path
                    for file_path in extracted_file_paths
                    if os.path.basename(file_path).startswith("Report_")
                ),
                None,
            )
            
            CLogger.MCWriteLog("info", f"File successfully downloaded, verified, and extracted to {self.strDateTimeFolderPath}.")
            CLogger.MCWriteLog("info", f"API call completed successfully with response.")
            if str(self.strVoucherType) == "BANK_STATEMENT":
                if strReportFilePath:
                    message = (
                        f"Document processing has been completed.\n\n"
                        f"Files have been saved and extracted to:\n"
                        f"{self.strDateTimeFolderPath}\n\n"
                        f"Excel report: {os.path.basename(strReportFilePath)}"
                    )

                    # Show the Excel file in a single PyQt5 window
                    CLogger.MCWriteLog("info", f"Report file found: {strReportFilePath}")
                    QMetaObject.invokeMethod(
                                self.csv_report_signal,
                                "showReportSignal",
                                Qt.QueuedConnection,
                                Q_ARG(str, strReportFilePath),
                                Q_ARG(str, message)
                            )
                    self.worker_thread.progress_update.emit(100,f"Step {self.intCurrentStep}/{ self.intTotalSteps}:The AI-enhanced document processing is now fully completed")
                    self.intCurrentStep+=1
                    self.dictInstalledExeDetail["CReqDeliveredTimeAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()
                    self.MSetClientRequestDeliveryAt()
                    return
                
                else:
                    self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")
                    QMetaObject.invokeMethod(
                        self.signal_handler,
                        "showPopupSignal",
                        Qt.QueuedConnection,
                        Q_ARG(str, "error"),
                        Q_ARG(str, "We couldn't process the provided bank statement transactions. Please manually enter the transactions into Tally.")
                    )
                    self.dictInstalledExeDetail["CReqDeliveredTimeAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()
                    self.MSetClientRequestDeliveryAt()
                    return

            if not strReportFilePath:
                self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")
                QMetaObject.invokeMethod(
                    self.signal_handler,
                    "showPopupSignal",
                    Qt.QueuedConnection,
                    Q_ARG(str, "error"),
                    Q_ARG(str, "We couldn't process the provided documents. Please manually enter the documents into Tally.")
                )

            else:
                # Professional message
                message = (
                    f"Document processing has been completed.\n\n"
                    f"Files have been saved and extracted to:\n"
                    f"{self.strDateTimeFolderPath}\n\n"
                    f"Excel report: {os.path.basename(strReportFilePath)}"
                )

                # Show the Excel file in a single PyQt5 window
                CLogger.MCWriteLog("info", f"Report file found: {strReportFilePath}")
                QMetaObject.invokeMethod(
                            self.csv_report_signal,
                            "showReportSignal",
                            Qt.QueuedConnection,
                            Q_ARG(str, strReportFilePath),
                            Q_ARG(str, message)
                        )
            # else:
            CLogger.MCWriteLog("INFO", f"API called with status code {response.status_code} and response: {response.text}.")
 
            self.worker_thread.progress_update.emit(100,f"Step {self.intCurrentStep}/{ self.intTotalSteps}:The AI-enhanced document processing is now fully completed")
            self.dictInstalledExeDetail["CReqDeliveredTimeAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()
            self.MSetClientRequestDeliveryAt()
            return


        except httpx.RequestError as e:
            CLogger.MCWriteLog("error", f"API request failed: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")

            QMetaObject.invokeMethod(
                self.signal_handler,
                "showPopupSignal",
                Qt.QueuedConnection,
                Q_ARG(str, "error"),
                Q_ARG(str, "We couldn't process the provided documents. Please manually enter the documents into Tally.")
            )
            self.dictInstalledExeDetail["CReqDeliveredTimeAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()
            self.MSetClientRequestDeliveryAt()
            return
            # raise ValueError(f"Failed to process documents, Please try again later.")

        except Exception as e:
            CLogger.MCWriteLog("error", f"API request failed: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")

            QMetaObject.invokeMethod(
                self.signal_handler,
                "showPopupSignal",
                Qt.QueuedConnection,
                Q_ARG(str, "error"),
                Q_ARG(str, "We couldn't process the provided documents. Please manually enter the documents into Tally.")
            )
            self.dictInstalledExeDetail["CReqDeliveredTimeAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()
            self.MSetClientRequestDeliveryAt()
            
            return
            # raise ValueError(f"Failed to process documents, Please try again later.")
    
    def MSProcessXMLFiles(self, extracted_files, strToken,lsalternative_bases):
        """
        Input:
            extracted_files: list
                List of extracted XML file paths to be processed.
            strToken: str
                Authorization token for making API calls to the AHM server.

        Output:
            dict: API response from the AHM server after sending the processed Tally response.

        Purpose:
            To process received XML files by sending them to the Tally server, generating response XML files,
            and then packaging and sending the response XML files to the AHM server.
        """
        lsReceivedXMLFiles = []
        # Filter XML files and count them
        xml_files = [f for f in extracted_files if f.endswith('.xml')]
        total_files = len(xml_files)
        processed_files = 0

        # Define progress range
        start_percent = 70
        end_percent = 99
        progress_range = end_percent - start_percent  # 29%

        # Process each XML file
        for xml_file_path in xml_files:
            response_path = os.path.join(self.strTallyResponseXMLDir, f"TallyXMLResponse-{os.path.basename(xml_file_path)}")
            # Create an empty response file
            with open(response_path, 'w') as file:
                file.write("")
            lsReceivedXMLFiles.append(response_path)
            
            # Process file with Tally server
            strExportedAbstractDataComment = CTallyExportHelper.MSExportTallyData(
                strRequestXMLPath=xml_file_path,
                strResponseXMLPath=response_path,
                url=self.strTallyServerUrl
            )
            CLogger.MCWriteLog("info", f"Processed {xml_file_path}: {strExportedAbstractDataComment}")
            
            # Increment processed files
            processed_files += 1
            
            # Calculate progress
            if total_files > 0:
                progress_percent = start_percent + int((processed_files / total_files) * progress_range)
                progress_percent = min(progress_percent, end_percent)  # Ensure it doesn’t exceed 99%
            else:
                progress_percent = start_percent  # No files, stay at 70%
            
            # Emit progress update
            self.worker_thread.progress_update.emit(
                progress_percent,
                "The document is being efficiently integrated into the Tally system through AI-powered automation."
            )

        # After all files are processed
        if total_files > 0:
            self.worker_thread.progress_update.emit(end_percent, "Sending responses to server")
        else:
            self.worker_thread.progress_update.emit(start_percent, "No files to process")

        # API call to AHM server
        strClientRespUrl = self.dictAPIEndPoints.get("ClientResp", "")
        try:
            strClientRespUrl=str(CArgumentParser.MSGet_working_endpoint(strClientRespUrl,lsalternative_bases))
            CLogger.MCWriteLog("info",f"Working URL selected: {strClientRespUrl}")
        except Exception as e:
            CLogger.MCWriteLog("error",f"Error: {str(e)}")
        
        dictAPIResponse = self.MSSendTallyImportedXMLResToServer(
            lsXMLFiles=lsReceivedXMLFiles,
            strToken=strToken,
            strURL=strClientRespUrl
        )
        
        
        return dictAPIResponse

    def MSSendTallyImportedXMLResToServer(self, lsXMLFiles, strToken, strURL):
        """
        Input:

            lsXMLFiles: list
                List of XML file paths to be sent to the AHM server.

            strToken: str
                Authorization token for making API calls to the AHM server.

        Output:
            
            dict or None:
                Dictionary containing the response from the AHM server if the call succeeds,
                or None if the call fails.

        Purpose:
            
            To send processed Tally XML response files as a ZIP archive to the AHM server
            and validate the server's response.

        Example:

            lsXMLFiles = ["path/to/response1.xml", "path/to/response2.xml"]
            response = obj.MSSendTallyImportedXMLResToServer(lsXMLFiles, "your_auth_token")
            print(response)

        """
        try:
            # Prepare data for checksum and comments
            self.dictInstalledExeDetail["ClientReqImportedAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()
            CTallyExportHelper.MSSendTallyImportedXMLResToServer(dictInstalledExeDetail = self.dictInstalledExeDetail, strTallyResponseXMLDir=self.strTallyResponseXMLDir, lsXMLFiles=lsXMLFiles, strURL=strURL, strToken=strToken)
        except httpx.RequestError as e:
            CLogger.MCWriteLog("error", f"API request failed: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")

        except Exception as e:
            CLogger.MCWriteLog("error", f"API request failed: {e}")
            CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
            self.worker_thread.progress_update.emit(100,"The AI-enhanced document processing is now fully completed")

    

# Test Run
if __name__ == "__main__":    
    CLogger.MCSetupLogging()
    app = QApplication([])

    

    # strUserToken = CLicenseHelper.MSVerifyLicense(license_file="license.lic")
    file_string = r"H:\\AI Data\\17_ParagTraders\\7_NEXION\\Invoice Till Date 19-11-24\\24-25-5673_4304.pdf"
    strUserToken = "gAAAAABndQ0H2IFUTFvP3N4IdvE9h99fzkFSdSS81h5WSH_IyIoLfuXNeQlfPXVIXJwcL8nZ-cY_6FupVv-GA0sP06xmvFOXn3wKKWTnrj0RHxmDcpw8CI0LKFS_sQQjZ-8wACfXBJIrVPL2lIqu97J6_PuWHVUiWmp0s_mhfyRI1fIgYA01yXakgaMqxmb8uGGppqeU2ObmHzS_2jNKRwuiAPKFuOeh94WZCXyys9aPSzwp_BJCn0NYBvewmifoV3eE2JLtWL481NswSxLSKzraOVpJpL-xWS0Jq0pWD51QLkp08LY1KGLcBxQMMVFIyZWBD1UNinuj"
    try:
        processor = CFileProcessor(file_string, strUserToken=strUserToken, bTestMode=True)
        processor.process_documents()
        app.exec_()

    except Exception as e:
        print(f"Error: {e}")

# if __name__ == "__main__":
#     # Example usage
#     # csv_path = r"H:\AI Data\DailyData\DeveloperTeam\2025_01_02\Reports\Report_20250102_134532.csv"
#     # message = (
#     #     "Document processing has been completed.\n\n"
#     #     "Files have been saved and extracted to:\n"
#     #     f"{csv_path}\n\n"
#     #     f"Report file: {os.path.basename(csv_path)}"
#     # )
#     # show_csv_report_dialog(csv_path, message)

#     # Test XML File Import, Send Response File to AHM Server
#     strUserToken = CLicenseHelper.MSVerifyLicense()
#     CLogger.MCSetupLogging()
#     objCFileProcessor = CFileProcessor(strUserToken=strUserToken, strCommand=r"H:\AI Data\DailyData\DeveloperTeam\2025_01_20\24-25-5673_4304.pdf", strVoucherType="Quotation",bTestMode=False)
#     objCFileProcessor.create_date_time_folder()
#     objCFileProcessor.MSProcessXMLFiles(extracted_files=[r"C:\Users\<USER>\Desktop\customer\REAL\Accuvelocity_exe\Requests\20250101_152752\183_20_01_2025.xml"], strToken=strUserToken)
