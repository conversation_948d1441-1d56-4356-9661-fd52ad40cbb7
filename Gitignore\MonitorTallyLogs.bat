@echo off
setlocal enabledelayedexpansion

set logfile=C:\TallyUsageLog.txt
echo Logging Tally.exe resource usage to %logfile% every 5 seconds. Press CTRL+C to stop.
echo.

:loop
rem Get timestamp
for /f "tokens=1-2 delims= " %%a in ("%date%") do set strDate=%%a
for /f "tokens=1-2 delims=:" %%a in ("%time%") do set strTime=%%a-%%b
set timestamp=%strDate%_%strTime%

rem Get tally.exe PID and memory usage
set tallyFound=false
for /f "skip=1 tokens=1,2 delims=," %%a in ('"wmic process where (name='tally.exe') get ProcessId^,WorkingSetSize /format:csv"') do (
    if not "%%a"=="" (
        set tallyFound=true
        set pid=%%b
        set mem=%%c
    )
)

if "!tallyFound!"=="true" (
    rem Convert WorkingSetSize (in bytes) to MB
    set /a memMB=!mem! / 1024 / 1024
    echo !timestamp! - tally.exe PID: !pid! - Memory Usage: !memMB! MB >> %logfile%
) else (
    echo !timestamp! - tally.exe not found >> %logfile%
)

timeout /nobreak /t 5 >nul
goto loop
