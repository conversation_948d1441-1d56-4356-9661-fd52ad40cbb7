# AccuVelocity Backup Report GUI

## Overview
The Backup Report GUI provides a comprehensive interface for viewing and managing backup file reports in AccuVelocity. It displays backup information organized by daily, weekly, and monthly views with professional formatting and export capabilities.

## Features

### 📊 **Tabbed Interface**
- **Daily Backups**: Shows all backups organized by date
- **Weekly Backups**: Groups backups by week (ISO week format)
- **Monthly Backups**: Groups backups by month

### 🎨 **Visual Enhancements**
- **Color Coding**: 
  - 🟢 Daily backups: Light green background
  - 🔵 Weekly backups: Light blue background
  - 🟡 Monthly backups: Light yellow background
- **Professional Table Layout**: Sortable columns with proper spacing
- **Alternating Row Colors**: Enhanced readability

### 📋 **Data Display**
Each backup entry shows:
- **Sr No**: Sequential number for easy reference
- **Backup Date/Period**: Date, week, or month identifier
- **Backup Name**: Generated backup filename
- **Backup Type**: Categorized as Daily, Weekly, or Monthly

### 🔄 **Functionality**
- **Refresh Data**: Updates backup information from the latest files
- **Export to TXT**: Generates comprehensive text reports
- **Professional Formatting**: Clean, organized display

## Usage

### Method 1: Command Line Launch
```bash
# Launch the backup report GUI directly
python Main.py --show-backup-report-gui
```

### Method 2: Programmatic Launch
```python
from CustomHelper import CBackupFileManager

# Show the backup report GUI
CBackupFileManager.MSShowBackupReportGUI()
```

### Method 3: Direct GUI Launch
```python
from BackupReportUI import show_backup_report

# Launch the GUI directly
show_backup_report()
```

### Method 4: Test Script
```bash
# Use the provided test script
python test_backup_report_gui.py
```

## File Structure

### Core Files
- `Sourcecode/BackupReportUI.py` - Main GUI implementation
- `Sourcecode/CustomHelper.py` - Backend logic and data processing
- `Resources/BackupFileTracking.json` - Raw backup tracking data
- `Resources/BackupFileReport.json` - Processed backup report data

### Supporting Files
- `test_backup_report_gui.py` - Test script for GUI functionality
- `BACKUP_REPORT_GUI_README.md` - This documentation file

## Data Flow

1. **Data Collection**: `MSTrackBackupRecord()` scans backup directories
2. **Report Generation**: `MSCreateBackUpFileReport()` processes and categorizes data
3. **GUI Display**: `CBackupReportWindow` loads and displays the processed data
4. **Export**: Text report generation with comprehensive formatting

## GUI Components

### Main Window
- **Title**: "AccuVelocity - Backup Report Viewer"
- **Size**: 1200x700 pixels (resizable)
- **Icon**: AccuVelocity logo
- **Layout**: Tabbed interface with control buttons

### Tables
- **Columns**: Sr No (80px), Date/Period (200px), Backup Name (300px), Type (150px)
- **Features**: Row selection, alternating colors, header sorting
- **Data**: Automatically populated from JSON reports

### Buttons
- **Refresh Data**: Regenerates backup reports and updates display
- **Export to TXT**: Creates formatted text report with timestamp
- **Close**: Closes the application

## Export Format

The TXT export includes:
- **Header**: Report title and generation timestamp
- **Daily Section**: All daily backups with details
- **Weekly Section**: All weekly backups with details  
- **Monthly Section**: All monthly backups with details
- **Summary**: Total counts for each category

### Sample Export Structure
```
================================================================================
ACCUVELOCITY BACKUP REPORT
================================================================================
Generated on: 2025-06-23 14:30:45

DAILY BACKUPS
--------------------------------------------------
Sr No    Date/Period          Backup Name                              Type      
--------------------------------------------------------------------------------
1        <USER>           <GROUP>                   Daily     
2        18-Jun-25           Backup_18-Jun-25_2043                   Daily     

WEEKLY BACKUPS
--------------------------------------------------
[Similar format for weekly data]

MONTHLY BACKUPS
--------------------------------------------------
[Similar format for monthly data]

SUMMARY
--------------------------------------------------
Total Daily Backups: 8
Total Weekly Backups: 7
Total Monthly Backups: 8
Grand Total: 23
```

## Error Handling

The GUI includes comprehensive error handling for:
- **Missing Files**: Graceful handling when backup files don't exist
- **JSON Parsing**: Safe loading of potentially corrupted data files
- **GUI Errors**: User-friendly error messages with logging
- **Export Failures**: File permission and path validation

## Dependencies

### Required Python Packages
- `PyQt5` - GUI framework
- `json` - Data processing
- `datetime` - Date/time handling
- `os` - File system operations

### Custom Modules
- `CustomHelper` - Backup management utilities
- `CustomLogger` - Logging functionality

## Troubleshooting

### Common Issues

1. **GUI Won't Launch**
   - Ensure PyQt5 is installed: `pip install PyQt5`
   - Check that all custom modules are in the Python path

2. **No Data Displayed**
   - Run backup tracking first: `python Main.py --track-backup-record`
   - Verify backup directory exists and contains data

3. **Export Fails**
   - Check file permissions in the target directory
   - Ensure sufficient disk space for report generation

4. **Import Errors**
   - Verify all source files are in the correct directories
   - Check Python path configuration

### Debug Mode
Enable detailed logging by setting up the logger before launching:
```python
from CustomLogger import CLogger
CLogger.MCSetupLogging(strLogsDirPath="Logs")
```

## Integration

The Backup Report GUI integrates seamlessly with:
- **Main Application**: Via command line arguments
- **Backup Manager**: Automatic data synchronization
- **Logging System**: Comprehensive error tracking
- **Resource Management**: Proper file and icon handling

## Future Enhancements

Potential improvements could include:
- **Filtering Options**: Date range and type filters
- **Search Functionality**: Find specific backups
- **Backup Restoration**: Direct restore from GUI
- **Scheduling**: Automated report generation
- **Email Reports**: Automated report distribution
