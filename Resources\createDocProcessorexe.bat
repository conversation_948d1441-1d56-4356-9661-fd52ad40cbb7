@echo off

:: =====================================================================
:: Batch Script to Package Python Script into Executable using PyInstaller
:: =====================================================================

:: Set the paths based on the project structure
SET "SCRIPT_RELATIVE_PATH=Sourcecode"
SET "OUTPUT_RELATIVE_PATH=exe\DocProcessor"

:: Set the name of the Python script and the desired exe name
SET "SCRIPT_NAME=Main.py"
SET "EXE_NAME=AVDocProcessor.exe"
SET "CONSOLE_EXE_NAME=DEB_AVDocProcessor.exe"

:: Check if Python is installed and PyInstaller is available
where python >nul 2>nul
IF ERRORLEVEL 1 (
    echo [ERROR] Python is not installed or not in PATH.
    pause
    exit /b 1
)

:: Check if PyInstaller is installed
where pyinstaller >nul 2>nul
IF ERRORLEVEL 1 (
    echo [ERROR] PyInstaller is not installed.
    pause
    exit /b 1
)

:: Validate directories
IF NOT EXIST "%SCRIPT_RELATIVE_PATH%" (
    echo [ERROR] Script directory "%SCRIPT_RELATIVE_PATH%" does not exist.
    pause
    exit /b 1
)

:: Create the output directory if it doesn't exist
IF NOT EXIST "%OUTPUT_RELATIVE_PATH%" (
    mkdir "%OUTPUT_RELATIVE_PATH%"
    IF ERRORLEVEL 1 (
        echo [ERROR] Failed to create output directory "%OUTPUT_RELATIVE_PATH%".
        pause
        exit /b 1
    )
)

:: Clean up previous builds if they exist
IF EXIST "%SCRIPT_RELATIVE_PATH%\build" (
    rmdir /s /q "%SCRIPT_RELATIVE_PATH%\build"
)

IF EXIST "%SCRIPT_RELATIVE_PATH%\dist" (
    rmdir /s /q "%SCRIPT_RELATIVE_PATH%\dist"
)

IF EXIST "%SCRIPT_RELATIVE_PATH%\%SCRIPT_NAME:.py=.spec%" (
    del "%SCRIPT_RELATIVE_PATH%\%SCRIPT_NAME:.py=.spec%"
)

:: Navigate to the script's directory
pushd "%SCRIPT_RELATIVE_PATH%"
IF ERRORLEVEL 1 (
    echo [ERROR] Failed to navigate to script directory "%SCRIPT_RELATIVE_PATH%".
    pause
    exit /b 1
)

:: Run PyInstaller to create the executable and related files with the specified output path
pyinstaller --windowed --onefile --distpath "..\%OUTPUT_RELATIVE_PATH%" --name "%EXE_NAME%" --icon="..\Resources\AvLogo.ico" --add-data="..\Resources\*;Resources" "%SCRIPT_NAME%"
pyinstaller --console --onefile --distpath "..\%OUTPUT_RELATIVE_PATH%" --name "%CONSOLE_EXE_NAME%" --icon="..\Resources\AvLogo.ico" --add-data="..\Resources\*;Resources" "%SCRIPT_NAME%"

:: Check if PyInstaller executed successfully
IF ERRORLEVEL 1 (
    echo [ERROR] PyInstaller encountered an error.
    popd
    pause
    exit /b 1
)

:: Return to the original directory
popd

:: Indicate success
echo [INFO] Executable and dependencies created successfully at "%OUTPUT_RELATIVE_PATH%".

:: Pause to keep the window open and show the result
pause
