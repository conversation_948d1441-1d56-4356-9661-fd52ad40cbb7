$logFile = "C:\TallyUsageLog.txt"
Write-Host "Logging tally.exe usage to $logFile every 5 seconds. Press CTRL+C to stop."

while ($true) {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

    $proc = Get-Process -Name "tally" -ErrorAction SilentlyContinue | Select-Object -First 1

    if ($proc) {
        $memMB     = [math]::Round($proc.WorkingSet64 / 1MB, 2)
        $commitMB  = [math]::Round($proc.PagedMemorySize64 / 1MB, 2)
        $ioReadMB  = [math]::Round($proc.IOReadBytes / 1MB, 2)
        $ioWriteMB = [math]::Round($proc.IOWriteBytes / 1MB, 2)

        $log = "$timestamp - PID: $($proc.Id) - RAM: $memMB MB - Commit: $commitMB MB - Disk Read: $ioReadMB MB - Disk Write: $ioWriteMB MB"
    } else {
        $log = "$timestamp - tally.exe not running"
    }

    Add-Content -Path $logFile -Value $log
    Start-Sleep -Seconds 5
}
