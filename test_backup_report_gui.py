#!/usr/bin/env python3
"""
Test script for the Backup Report GUI
This script demonstrates how to launch the backup report viewer G<PERSON>.
"""

import sys
import os

# Add the Sourcecode directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Sourcecode'))

def test_backup_report_gui():
    """Test the backup report GUI"""
    try:
        from BackupReportUI import show_backup_report
        
        print("Launching Backup Report GUI...")
        print("This will show daily, weekly, and monthly backup reports in a tabbed interface.")
        print("Features:")
        print("- View backups organized by day, week, and month")
        print("- Color-coded backup types (Daily=Green, Weekly=Blue, Monthly=Yellow)")
        print("- Refresh data functionality")
        print("- Export to TXT report functionality")
        print("- Professional tabular display with sorting")
        
        # Launch the GUI
        show_backup_report()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure all required modules are available.")
    except Exception as e:
        print(f"Error launching backup report GUI: {e}")

def test_via_main():
    """Test via the main application with command line argument"""
    try:
        # Import the main module
        from Main import main
        
        # Simulate command line argument
        sys.argv = ['Main.py', '--show-backup-report-gui']
        
        print("Testing via main application...")
        main()
        
    except Exception as e:
        print(f"Error testing via main: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("ACCUVELOCITY BACKUP REPORT GUI TEST")
    print("=" * 60)
    
    choice = input("Choose test method:\n1. Direct GUI launch\n2. Via main application\nEnter choice (1 or 2): ")
    
    if choice == "1":
        test_backup_report_gui()
    elif choice == "2":
        test_via_main()
    else:
        print("Invalid choice. Running direct GUI launch...")
        test_backup_report_gui()
