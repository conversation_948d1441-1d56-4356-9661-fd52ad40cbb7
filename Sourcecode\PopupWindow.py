
from PyQt5.QtWidgets import <PERSON>Widget, QVBoxLayout, QLabel, QPushButton, QMessageBox, QApplication
from PyQt5.QtGui import QIcon
from myFileHelper import CFileHelper
import os

class CPopupWindow(QWidget):
    def __init__(self, message, message_type, title="Accuvelocity Document Processor"):
        super().__init__()
        self.title = title
        self.message = message
        self.message_type = message_type
        self.initUI()

    def initUI(self):
        self.setWindowTitle(self.title)
        strExeDirectory = CFileHelper._get_executable_directory()
        strIconPath = os.path.join(strExeDirectory, "Resources", "AvLogo.ico")
        self.setWindowIcon(QIcon(strIconPath))  # Set icon for the popup window

        self.setGeometry(100, 100, 400, 200)

        layout = QVBoxLayout()

        self.label = QLabel(self.message)
        layout.addWidget(self.label)

        self.ok_button = QPushButton('OK')
        self.ok_button.clicked.connect(self.close)
        layout.addWidget(self.ok_button)

        self.setLayout(layout)

        self.show_message(self.message_type)

    def show_message(self, message_type):
        msg = QMessageBox()
        if message_type == "info":
            msg.setIcon(QMessageBox.Information)
        elif message_type == "error":
            msg.setIcon(QMessageBox.Critical)
        else:
            msg.setIcon(QMessageBox.Warning)
        msg.setWindowTitle(self.title)
        msg.setText(self.message)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()

if __name__ == "__main__":
    app = QApplication([])
    CPopupWindow(title="Test", message="Test Message", message_type="error")
    app.exec_()