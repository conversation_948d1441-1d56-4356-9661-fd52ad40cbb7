import sys
import os
import json
import socket
import platform
import uuid
import shutil
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QLabel, QApplication, QHeaderView, QDialog, QPushButton, QTabWidget,
    QDesktopWidget, QFileDialog, QMessageBox, QFrame, QTextEdit
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

# Import custom modules
from CustomHelper import CGeneralHelper, CBackupFileManager
from CustomLogger import CLogger

class DateTableWidgetItem(QTableWidgetItem):
    """Custom table widget item for proper date sorting"""
    def __init__(self, date_str):
        super().__init__(date_str)
        self.date_str = date_str
        try:
            # Convert date string to datetime for proper sorting
            self.date_obj = datetime.strptime(date_str, "%d-%b-%y")
        except:
            self.date_obj = datetime.min

    def __lt__(self, other):
        if isinstance(other, DateTableWidgetItem):
            return self.date_obj < other.date_obj
        return super().__lt__(other)


class CBackupReportWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.backup_data = {}
        self.backup_directory = ""
        self.total_size_bytes = 0
        self.initUI()
        self.load_backup_data()
        self.populate_table()

    def initUI(self):
        """Initialize the user interface"""
        # Calculate total size first for title
        self.calculate_total_size()

        # Set window title with total size
        size_str = self.format_size(self.total_size_bytes)
        self.setWindowTitle(f"AccuVelocity - Backup Report Viewer (Total: {size_str})")

        # Calculate window width to fit table exactly without extra space
        # Table columns: Sr No(60) + Date(120) + Name(450) + Size(100) + Actions(140) = 870
        table_width = 60 + 120 + 450 + 100 + 140  # Total = 870
        window_width = table_width + 50  # Add minimal margins = 920
        self.setGeometry(100, 100, window_width, 650)

        # Center the window on screen
        self.center_window()

        # Set window icon
        try:
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            strIconPath = os.path.join(strResourceDirpath, "AvLogo.ico")
            self.setWindowIcon(QIcon(strIconPath))
        except:
            pass

        # Enable window buttons
        self.setWindowFlags(Qt.Window | Qt.WindowTitleHint | Qt.WindowCloseButtonHint |
                           Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

        # Apply sophisticated professional styling
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1E2A44, stop:0.5 #2A3A55, stop:1 #1E2A44);
                border-radius: 15px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border-radius: 12px;
                padding: 10px 18px;
                font-weight: 600;
                border: none;
                font-size: 12px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #1E88E5;
                border: 1px solid #0d47a1;
            }
            QPushButton:pressed {
                background-color: #1976D2;
            }
        """)

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Add title with total size
        title_label = QLabel(f"Backup File Report - Total Size: {size_str}")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)

        # Create single table for all backups
        self.backup_table = QTableWidget()
        self.backup_table.setColumnCount(5)
        self.backup_table.setHorizontalHeaderLabels([
            "Sr No", "Backup Date", "Backup Name", "Size (MB)", "Actions"
        ])

        # Set table properties
        self.setup_table(self.backup_table)

        # Disable sorting initially to prevent issues with custom date format
        self.backup_table.setSortingEnabled(False)

        main_layout.addWidget(self.backup_table)

        # Add buttons
        button_layout = QHBoxLayout()

        # Refresh button with professional styling
        self.refresh_button = QPushButton("Refresh Data")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border-radius: 10px;
                padding: 10px 20px;
                font-weight: bold;
                border: none;
                font-size: 12px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_data)
        button_layout.addWidget(self.refresh_button)

        # Export to TXT button with professional styling
        self.export_button = QPushButton("Export to TXT Report")
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border-radius: 10px;
                padding: 10px 20px;
                font-weight: bold;
                border: none;
                font-size: 12px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
            QPushButton:pressed {
                background-color: #ef6c00;
            }
        """)
        self.export_button.clicked.connect(self.export_to_txt)
        button_layout.addWidget(self.export_button)

        # View Backup Directory button with professional styling
        self.view_location_button = QPushButton("View Backup Location")
        self.view_location_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 10px;
                padding: 10px 20px;
                font-weight: bold;
                border: none;
                font-size: 12px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:pressed {
                background-color: #117a8b;
            }
        """)
        self.view_location_button.clicked.connect(self.view_backup_location)
        button_layout.addWidget(self.view_location_button)

        button_layout.addStretch()

        # Close button with professional styling
        self.close_button = QPushButton("Close")
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 10px;
                padding: 10px 20px;
                font-weight: bold;
                border: none;
                font-size: 12px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #545b62;
            }
        """)
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)

        main_layout.addLayout(button_layout)

        # Add "Powered by AccuVelocity AI" footer
        footer_label = QLabel("Powered by AccuVelocity AI")
        footer_label.setAlignment(Qt.AlignCenter)
        footer_font = QFont("Arial", 12, QFont.Bold)
        footer_font.setPointSize(10)
        footer_font.setItalic(True)
        footer_label.setFont(footer_font)
        footer_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: transparent;
                padding: 10px; 
                margin-top: 10px;
                letter-spacing: 1px;
            }
        """)
        main_layout.addWidget(footer_label)

    def center_window(self):
        """Center the window on the screen"""
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        self.move(int((screen.width() - size.width()) / 2),
                  int((screen.height() - size.height()) / 2))

    def calculate_total_size(self):
        """Calculate total size of all backups"""
        try:
            self.backup_directory = CBackupFileManager.MSGetBackupDirectory()
            if not self.backup_directory or not os.path.exists(self.backup_directory):
                self.total_size_bytes = 0
                return

            total_size = 0
            for root, _, files in os.walk(self.backup_directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        continue

            self.total_size_bytes = total_size
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to calculate total backup size: {e}")
            self.total_size_bytes = 0

    def format_size(self, size_bytes):
        """Format size in bytes to human readable format"""
        if size_bytes == 0:
            return "0 B"

        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.2f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.2f} PB"

    def get_backup_size(self, date_str, time_str):
        """Get size of specific backup"""
        try:
            if not self.backup_directory:
                return 0

            backup_path = os.path.join(self.backup_directory, date_str, time_str)
            if not os.path.exists(backup_path):
                return 0

            total_size = 0
            for root, _, files in os.walk(backup_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        continue

            return total_size
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to get backup size for {date_str}/{time_str}: {e}")
            return 0

    def view_backup_location(self):
        """Open backup directory in file explorer"""
        try:
            if not self.backup_directory or not os.path.exists(self.backup_directory):
                QMessageBox.warning(self, "Warning", "Backup directory not found or not accessible.")
                return

            # Open directory in file explorer
            if os.name == 'nt':  # Windows
                os.startfile(self.backup_directory)
            elif os.name == 'posix':  # macOS and Linux
                os.system(f'open "{self.backup_directory}"' if sys.platform == 'darwin' else f'xdg-open "{self.backup_directory}"')

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to open backup location: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open backup location: {str(e)}")

    def view_specific_backup(self, date_str, time_str):
        """Open specific backup directory"""
        try:
            backup_path = os.path.join(self.backup_directory, date_str, time_str)
            if not os.path.exists(backup_path):
                QMessageBox.warning(self, "Warning", f"Backup directory not found: {backup_path}")
                return

            # Open directory in file explorer
            if os.name == 'nt':  # Windows
                os.startfile(backup_path)
            elif os.name == 'posix':  # macOS and Linux
                os.system(f'open "{backup_path}"' if sys.platform == 'darwin' else f'xdg-open "{backup_path}"')

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to open specific backup location: {e}")
            QMessageBox.critical(self, "Error", f"Failed to open backup location: {str(e)}")

    def setup_table(self, table):
        """Setup common table properties with professional styling"""
        # Set alternating row colors
        table.setAlternatingRowColors(True)

        # Set selection behavior
        table.setSelectionBehavior(QTableWidget.SelectRows)

        # Set row height for proper text and button visibility
        table.verticalHeader().setDefaultSectionSize(47)  # Increased height for better visibility
        table.verticalHeader().setVisible(False)  # Hide row numbers

        # Disable default sorting to handle custom date sorting
        table.setSortingEnabled(False)

        # Set header properties
        header = table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(QHeaderView.Interactive)

        # Set column widths to match window width exactly (total = 870px)
        table.setColumnWidth(0, 60)   # Sr No
        table.setColumnWidth(1, 120)  # Backup Date
        table.setColumnWidth(2, 300)  # Backup Name (expanded)
        table.setColumnWidth(3, 100)  # Size
        table.setColumnWidth(4, 315)  # Actions (exact fit for buttons)

        # Configure scroll bars - disable horizontal since window fits table exactly
        table.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Remove extra space by disabling stretch on last section
        table.horizontalHeader().setStretchLastSection(False)

        # Apply sophisticated professional styling
        table.setStyleSheet("""
            QTableWidget {
                background: rgba(255, 255, 255, 0.95);
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                gridline-color: #dee2e6;
                border-radius: 12px;
                border: 2px solid #4a6491;
                font-size: 12px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34495e, stop:1 #2c3e50);
                color: white;
                padding: 12px 8px;
                border: none;
                border-right: 1px solid #34495e;
                font-weight: bold;
                font-size: 12px;
                text-align: center;
            }
            QHeaderView::section:first {
                border-top-left-radius: 10px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 10px;
                border-right: none;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
                border-right: 1px solid #e9ecef;
            }
            QTableWidget::item:hover {
                background-color: #f1f3f4;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1565c0;
            }
            QScrollBar:vertical {
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #6c757d;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #495057;
            }
        """)

    def load_backup_data(self):
        """Load backup data from JSON file"""
        try:
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            report_file = os.path.join(strResourceDirpath, "BackupFileReport.json")
            
            if os.path.exists(report_file):
                with open(report_file, "r") as f:
                    self.backup_data = json.load(f)
            else:
                self.backup_data = {"DayWise": {}, "WeekWise": {}, "MonthWise": {}}
                
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to load backup data: {e}")
            self.backup_data = {"DayWise": {}, "WeekWise": {}, "MonthWise": {}}

    def determine_backup_type(self, date_str):
        """Determine backup type based on frequency"""
        try:
            dt = datetime.strptime(date_str, "%d-%b-%y")
            current_date = datetime.now().date()
            backup_date = dt.date()

            days_diff = (current_date - backup_date).days

            if days_diff <= 1:
                return "Daily"
            elif days_diff <= 7:
                return "Weekly"
            else:
                return "Monthly"
        except:
            return "Daily"

    def populate_table(self):
        """Populate the backup table with all backup data"""
        daily_data = self.backup_data.get("DayWise", {})

        if not daily_data:
            # Show message for no backups found
            self.backup_table.setRowCount(1)
            no_data_item = QTableWidgetItem("No backup files found in the backup directory.")
            no_data_item.setTextAlignment(Qt.AlignCenter)
            self.backup_table.setItem(0, 0, no_data_item)
            self.backup_table.setSpan(0, 0, 1, 5)  # Span across all columns (updated for 5 columns)
            return

        # Calculate total rows needed
        total_rows = sum(len(data["timestamps"]) for data in daily_data.values())
        self.backup_table.setRowCount(total_rows)

        row = 0
        sr_no = 1

        # Sort dates in descending order (newest first)
        sorted_dates = sorted(daily_data.keys(), key=lambda x: datetime.strptime(x, "%d-%b-%y"), reverse=True)

        for date_str in sorted_dates:
            data = daily_data[date_str]
            timestamps = sorted(data["timestamps"], reverse=True)  # Latest time first

            for time_str in timestamps:
                backup_name = f"Backup_{date_str}_{time_str}"
                backup_size = self.get_backup_size(date_str, time_str)
                size_str = self.format_size(backup_size)

                # Set table items (removed backup type column)
                self.backup_table.setItem(row, 0, QTableWidgetItem(str(sr_no)))
                self.backup_table.setItem(row, 1, DateTableWidgetItem(date_str))  # Use custom date item for sorting
                self.backup_table.setItem(row, 2, QTableWidgetItem(backup_name))
                self.backup_table.setItem(row, 3, QTableWidgetItem(size_str))

                # Create simple text label buttons with 10px padding
                action_widget = QWidget()
                action_widget.setStyleSheet("background: transparent;")
                action_layout = QHBoxLayout(action_widget)
                action_layout.setContentsMargins(10, 0, 10, 0)
                action_layout.setSpacing(10)
                action_widget.setLayout(action_layout)
                # action_layout.setContentsMargins(5, 5, 5, 5)
                # action_layout.setSpacing(5)

                # Simple View button with 10px padding on both sides
                view_button = QPushButton("View")
                view_button.setStyleSheet("""
                    QPushButton {
                        background-color: #6c757d;
                        color: white;
                        width:10px;
                        padding: 5px 10px;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #5a6268;
                    }
                    QPushButton:pressed {
                        background-color: #545b62;
                    }
                """)
                view_button.setText("View")
                view_button.setFixedHeight(30)
                view_button.setFixedWidth(5)
                view_button.clicked.connect(lambda _, d=date_str, t=time_str: self.view_specific_backup(d, t))
                action_layout.addWidget(view_button)

                # Simple Delete button with 10px padding on both sides
                delete_button = QPushButton("Delete")
                delete_button.setStyleSheet("""
                    QPushButton {
                        background-color: #dc3545;
                        color: white;
                        padding: 5px 10px;
                        width:10px;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #c82333;
                    }
                    QPushButton:pressed {
                        background-color: #bd2130;
                    }
                """)
                delete_button.setText("Delete")
                delete_button.setFixedHeight(30)
                delete_button.setFixedWidth(5)
                delete_button.clicked.connect(lambda _, d=date_str, t=time_str, bn=backup_name: self.delete_backup(d, t, bn))
                action_layout.addWidget(delete_button)

                self.backup_table.setCellWidget(row, 4, action_widget)

                row += 1
                sr_no += 1

        # Enable sorting after populating the table
        self.backup_table.setSortingEnabled(True)

        # Sort by date column in descending order (newest first)
        self.backup_table.sortItems(1, Qt.DescendingOrder)

    def delete_backup(self, date_str, time_str, backup_name):
        """Delete a backup with confirmation dialog"""
        try:
            # Get backup location
            backup_path = os.path.join(self.backup_directory, date_str, time_str)

            # Show confirmation dialog
            reply = QMessageBox.question(
                self,
                'Confirm Deletion',
                f'Are you sure you want to delete backup?\n\n'
                f'Backup Name: {backup_name}\n'
                f'Backup Location: {backup_path}\n\n'
                f'This action cannot be undone.',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Delete the backup directory
                if os.path.exists(backup_path):
                    import shutil
                    shutil.rmtree(backup_path)

                    # Update the backup tracking JSON
                    self.update_backup_tracking_after_deletion(date_str, time_str)

                    # Refresh the table
                    self.refresh_data()

                    # Show success message
                    QMessageBox.information(
                        self,
                        'Backup Deleted',
                        f'Backup "{backup_name}" has been successfully deleted.'
                    )

                    CLogger.MCWriteLog("info", f"Backup deleted successfully: {backup_name}")
                else:
                    QMessageBox.warning(
                        self,
                        'Backup Not Found',
                        f'Backup directory not found:\n{backup_path}'
                    )

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to delete backup {backup_name}: {e}")
            QMessageBox.critical(
                self,
                'Deletion Failed',
                f'Failed to delete backup "{backup_name}".\n\nError: {str(e)}'
            )

    def update_backup_tracking_after_deletion(self, date_str, time_str):
        """Update BackupFileTracking.json after backup deletion"""
        try:
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            tracking_file = os.path.join(strResourceDirpath, "BackupFileTracking.json")

            if os.path.exists(tracking_file):
                with open(tracking_file, 'r') as f:
                    tracking_data = json.load(f)

                # Remove the timestamp from DayWise data
                if date_str in tracking_data.get("DayWise", {}):
                    if time_str in tracking_data["DayWise"][date_str]["timestamps"]:
                        tracking_data["DayWise"][date_str]["timestamps"].remove(time_str)

                        # If no more timestamps for this date, remove the date entry
                        if not tracking_data["DayWise"][date_str]["timestamps"]:
                            del tracking_data["DayWise"][date_str]

                # Save updated tracking data
                with open(tracking_file, 'w') as f:
                    json.dump(tracking_data, f, indent=4)

                # Reload backup data
                self.load_backup_data()

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to update backup tracking after deletion: {e}")

    def get_system_info(self):
        """Get system information for report header"""
        try:
            device_name = socket.gethostname()
            system_name = platform.system()
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            request_id = f"REQ_{uuid.uuid4().hex[:8].upper()}"

            return {
                "device_name": device_name,
                "system_name": system_name,
                "current_time": current_time,
                "request_id": request_id
            }
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to get system info: {e}")
            return {
                "device_name": "Unknown",
                "system_name": "Unknown",
                "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "request_id": "REQ_UNKNOWN"
            }

    def refresh_data(self):
        """Refresh the backup data and tables"""
        try:
            # Regenerate the backup report
            CBackupFileManager.MSCreateBackUpFileReport()

            # Recalculate total size
            self.calculate_total_size()

            # Update window title with new size
            size_str = self.format_size(self.total_size_bytes)
            self.setWindowTitle(f"AccuVelocity - Backup Report Viewer (Total: {size_str})")

            # Reload data
            self.load_backup_data()

            # Repopulate table
            self.populate_table()

            QMessageBox.information(self, "Success", "Backup data refreshed successfully!")

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to refresh backup data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to refresh backup data: {str(e)}")

    def export_to_txt(self):
        """Export backup report to a text file"""
        try:
            # Get save location
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            default_filename = f"BackupReport_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            default_path = os.path.join(strResourceDirpath, default_filename)
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "Save Backup Report", 
                default_path, 
                "Text Files (*.txt);;All Files (*)"
            )
            
            if file_path:
                self.generate_txt_report(file_path)
                QMessageBox.information(self, "Success", f"Report exported successfully to:\n{file_path}")
                
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to export backup report: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export report: {str(e)}")

    def generate_txt_report(self, file_path):
        """Generate the text report content"""
        system_info = self.get_system_info()

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("=" * 100 + "\n")
            f.write("ACCUVELOCITY BACKUP REPORT\n")
            f.write("=" * 100 + "\n")
            f.write(f"Generated on: {system_info['current_time']}\n")
            f.write(f"Device Name: {system_info['device_name']}\n")
            f.write(f"System Name: {system_info['system_name']}\n")
            f.write(f"Request ID: {system_info['request_id']}\n")
            f.write(f"Total Backup Size: {self.format_size(self.total_size_bytes)}\n")
            f.write(f"Backup Directory: {self.backup_directory}\n\n")

            # Check if there are any backups
            if self.backup_table.rowCount() == 0 or (self.backup_table.rowCount() == 1 and
                self.backup_table.item(0, 0) and "No backup files found" in self.backup_table.item(0, 0).text()):
                f.write("NO BACKUP FILES FOUND\n")
                f.write("-" * 50 + "\n")
                f.write("No backup files were found in the backup directory.\n")
                f.write("Please ensure that backups are being created properly.\n\n")
                return

            # All backups section
            f.write("ALL BACKUP FILES\n")
            f.write("-" * 100 + "\n")
            self.write_table_to_file(f)

            # Summary section
            f.write("\n\nSUMMARY\n")
            f.write("-" * 50 + "\n")

            f.write(f"Grand Total: {self.backup_table.rowCount()}\n")
            f.write(f"Total Storage Used: {self.format_size(self.total_size_bytes)}\n")

    def write_table_to_file(self, file_handle):
        """Write table data to file"""
        if self.backup_table.rowCount() == 0:
            file_handle.write("No backups found.\n")
            return

        # Write header
        file_handle.write(f"{'Sr No':<8} {'Date':<15} {'Backup Name':<40} {'Type':<10} {'Size':<12}\n")
        file_handle.write("-" * 100 + "\n")

        # Write data rows
        for row in range(self.backup_table.rowCount()):
            sr_no = self.backup_table.item(row, 0).text() if self.backup_table.item(row, 0) else ""
            date = self.backup_table.item(row, 1).text() if self.backup_table.item(row, 1) else ""
            backup_name = self.backup_table.item(row, 2).text() if self.backup_table.item(row, 2) else ""
            backup_type = self.backup_table.item(row, 3).text() if self.backup_table.item(row, 3) else ""
            size = self.backup_table.item(row, 4).text() if self.backup_table.item(row, 4) else ""

            # Skip if this is the "no data" row
            if "No backup files found" in sr_no:
                continue

            file_handle.write(f"{sr_no:<8} {date:<15} {backup_name:<40} {backup_type:<10} {size:<12}\n")


def show_backup_report():
    """Standalone function to show backup report"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = CBackupReportWindow()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    show_backup_report()
