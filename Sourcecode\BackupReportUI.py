import sys
import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, 
    QLabel, QApplication, QHeaderView, QDialog, QPushButton, QTabWidget,
    QDesktopWidget, QFileDialog, QMessageBox, QFrame, QTextEdit
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QIcon

# Import custom modules
from CustomHelper import CGeneralHelper
from CustomLogger import CLogger


class CBackupReportWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.backup_data = {}
        self.initUI()
        self.load_backup_data()
        self.populate_tables()

    def initUI(self):
        """Initialize the user interface"""
        self.setWindowTitle("AccuVelocity - Backup Report Viewer")
        self.setGeometry(100, 100, 1200, 700)
        
        # Center the window on screen
        self.center_window()
        
        # Set window icon
        try:
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            strIconPath = os.path.join(strResourceDirpath, "AvLogo.ico")
            self.setWindowIcon(QIcon(strIconPath))
        except:
            pass
        
        # Enable window buttons
        self.setWindowFlags(Qt.Window | Qt.WindowTitleHint | Qt.WindowCloseButtonHint |
                           Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)
        
        # Create main layout
        main_layout = QVBoxLayout(self)
        
        # Add title
        title_label = QLabel("Backup File Report")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_daily_tab()
        self.create_weekly_tab()
        self.create_monthly_tab()
        
        # Add buttons
        button_layout = QHBoxLayout()
        
        # Refresh button
        self.refresh_button = QPushButton("Refresh Data")
        self.refresh_button.clicked.connect(self.refresh_data)
        button_layout.addWidget(self.refresh_button)
        
        # Export to TXT button
        self.export_button = QPushButton("Export to TXT Report")
        self.export_button.clicked.connect(self.export_to_txt)
        button_layout.addWidget(self.export_button)
        
        button_layout.addStretch()
        
        # Close button
        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        main_layout.addLayout(button_layout)

    def center_window(self):
        """Center the window on the screen"""
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        self.move(int((screen.width() - size.width()) / 2),
                  int((screen.height() - size.height()) / 2))

    def create_daily_tab(self):
        """Create the daily backup tab"""
        daily_widget = QWidget()
        layout = QVBoxLayout(daily_widget)
        
        # Create table
        self.daily_table = QTableWidget()
        self.daily_table.setColumnCount(4)
        self.daily_table.setHorizontalHeaderLabels(["Sr No", "Backup Date", "Backup Name", "Backup Type"])
        
        # Set table properties
        self.setup_table(self.daily_table)
        layout.addWidget(self.daily_table)
        
        self.tab_widget.addTab(daily_widget, "Daily Backups")

    def create_weekly_tab(self):
        """Create the weekly backup tab"""
        weekly_widget = QWidget()
        layout = QVBoxLayout(weekly_widget)
        
        # Create table
        self.weekly_table = QTableWidget()
        self.weekly_table.setColumnCount(4)
        self.weekly_table.setHorizontalHeaderLabels(["Sr No", "Week", "Backup Name", "Backup Type"])
        
        # Set table properties
        self.setup_table(self.weekly_table)
        layout.addWidget(self.weekly_table)
        
        self.tab_widget.addTab(weekly_widget, "Weekly Backups")

    def create_monthly_tab(self):
        """Create the monthly backup tab"""
        monthly_widget = QWidget()
        layout = QVBoxLayout(monthly_widget)
        
        # Create table
        self.monthly_table = QTableWidget()
        self.monthly_table.setColumnCount(4)
        self.monthly_table.setHorizontalHeaderLabels(["Sr No", "Month", "Backup Name", "Backup Type"])
        
        # Set table properties
        self.setup_table(self.monthly_table)
        layout.addWidget(self.monthly_table)
        
        self.tab_widget.addTab(monthly_widget, "Monthly Backups")

    def setup_table(self, table):
        """Setup common table properties"""
        # Set alternating row colors
        table.setAlternatingRowColors(True)
        
        # Set selection behavior
        table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # Set header properties
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # Set column widths
        table.setColumnWidth(0, 80)   # Sr No
        table.setColumnWidth(1, 200)  # Date/Week/Month
        table.setColumnWidth(2, 300)  # Backup Name
        table.setColumnWidth(3, 150)  # Backup Type

    def load_backup_data(self):
        """Load backup data from JSON file"""
        try:
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            report_file = os.path.join(strResourceDirpath, "BackupFileReport.json")
            
            if os.path.exists(report_file):
                with open(report_file, "r") as f:
                    self.backup_data = json.load(f)
            else:
                self.backup_data = {"DayWise": {}, "WeekWise": {}, "MonthWise": {}}
                
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to load backup data: {e}")
            self.backup_data = {"DayWise": {}, "WeekWise": {}, "MonthWise": {}}

    def determine_backup_type(self, date_str, time_str):
        """Determine backup type based on frequency"""
        try:
            dt = datetime.strptime(date_str, "%d-%b-%y")
            current_date = datetime.now().date()
            backup_date = dt.date()
            
            days_diff = (current_date - backup_date).days
            
            if days_diff <= 1:
                return "Daily"
            elif days_diff <= 7:
                return "Weekly"
            else:
                return "Monthly"
        except:
            return "Daily"

    def populate_tables(self):
        """Populate all tables with backup data"""
        self.populate_daily_table()
        self.populate_weekly_table()
        self.populate_monthly_table()

    def populate_daily_table(self):
        """Populate the daily backup table"""
        daily_data = self.backup_data.get("DayWise", {})
        
        # Calculate total rows needed
        total_rows = sum(len(data["timestamps"]) for data in daily_data.values())
        self.daily_table.setRowCount(total_rows)
        
        row = 0
        sr_no = 1
        
        # Sort dates
        sorted_dates = sorted(daily_data.keys(), key=lambda x: datetime.strptime(x, "%d-%b-%y"), reverse=True)
        
        for date_str in sorted_dates:
            data = daily_data[date_str]
            timestamps = sorted(data["timestamps"])
            
            for time_str in timestamps:
                backup_name = f"Backup_{date_str}_{time_str}"
                backup_type = self.determine_backup_type(date_str, time_str)
                
                self.daily_table.setItem(row, 0, QTableWidgetItem(str(sr_no)))
                self.daily_table.setItem(row, 1, QTableWidgetItem(date_str))
                self.daily_table.setItem(row, 2, QTableWidgetItem(backup_name))
                self.daily_table.setItem(row, 3, QTableWidgetItem(backup_type))
                
                # Color code backup types
                self.color_code_row(self.daily_table, row, backup_type)
                
                row += 1
                sr_no += 1

    def populate_weekly_table(self):
        """Populate the weekly backup table"""
        weekly_data = self.backup_data.get("WeekWise", {})
        
        # Calculate total rows needed
        total_rows = sum(len(times) for times in weekly_data.values())
        self.weekly_table.setRowCount(total_rows)
        
        row = 0
        sr_no = 1
        
        # Sort weeks
        sorted_weeks = sorted(weekly_data.keys(), reverse=True)
        
        for week_str in sorted_weeks:
            timestamps = sorted(weekly_data[week_str])
            
            for time_str in timestamps:
                backup_name = f"Backup_{week_str}_{time_str}"
                backup_type = "Weekly"
                
                self.weekly_table.setItem(row, 0, QTableWidgetItem(str(sr_no)))
                self.weekly_table.setItem(row, 1, QTableWidgetItem(week_str))
                self.weekly_table.setItem(row, 2, QTableWidgetItem(backup_name))
                self.weekly_table.setItem(row, 3, QTableWidgetItem(backup_type))
                
                # Color code backup types
                self.color_code_row(self.weekly_table, row, backup_type)
                
                row += 1
                sr_no += 1

    def populate_monthly_table(self):
        """Populate the monthly backup table"""
        monthly_data = self.backup_data.get("MonthWise", {})
        
        # Calculate total rows needed
        total_rows = sum(len(times) for times in monthly_data.values())
        self.monthly_table.setRowCount(total_rows)
        
        row = 0
        sr_no = 1
        
        # Sort months
        sorted_months = sorted(monthly_data.keys(), reverse=True)
        
        for month_str in sorted_months:
            timestamps = sorted(monthly_data[month_str])
            
            for time_str in timestamps:
                backup_name = f"Backup_{month_str}_{time_str}"
                backup_type = "Monthly"
                
                self.monthly_table.setItem(row, 0, QTableWidgetItem(str(sr_no)))
                self.monthly_table.setItem(row, 1, QTableWidgetItem(month_str))
                self.monthly_table.setItem(row, 2, QTableWidgetItem(backup_name))
                self.monthly_table.setItem(row, 3, QTableWidgetItem(backup_type))
                
                # Color code backup types
                self.color_code_row(self.monthly_table, row, backup_type)
                
                row += 1
                sr_no += 1

    def color_code_row(self, table, row, backup_type):
        """Color code table rows based on backup type"""
        if backup_type == "Daily":
            color = QColor(200, 255, 200)  # Light green
        elif backup_type == "Weekly":
            color = QColor(200, 200, 255)  # Light blue
        else:  # Monthly
            color = QColor(255, 255, 200)  # Light yellow
        
        for col in range(table.columnCount()):
            item = table.item(row, col)
            if item:
                item.setBackground(color)

    def refresh_data(self):
        """Refresh the backup data and tables"""
        try:
            # Regenerate the backup report
            from CustomHelper import CBackupFileManager
            CBackupFileManager.MSCreateBackUpFileReport()
            
            # Reload data
            self.load_backup_data()
            
            # Repopulate tables
            self.populate_tables()
            
            QMessageBox.information(self, "Success", "Backup data refreshed successfully!")
            
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to refresh backup data: {e}")
            QMessageBox.critical(self, "Error", f"Failed to refresh backup data: {str(e)}")

    def export_to_txt(self):
        """Export backup report to a text file"""
        try:
            # Get save location
            strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
            default_filename = f"BackupReport_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            default_path = os.path.join(strResourceDirpath, default_filename)
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "Save Backup Report", 
                default_path, 
                "Text Files (*.txt);;All Files (*)"
            )
            
            if file_path:
                self.generate_txt_report(file_path)
                QMessageBox.information(self, "Success", f"Report exported successfully to:\n{file_path}")
                
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to export backup report: {e}")
            QMessageBox.critical(self, "Error", f"Failed to export report: {str(e)}")

    def generate_txt_report(self, file_path):
        """Generate the text report content"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("ACCUVELOCITY BACKUP REPORT\n")
            f.write("=" * 80 + "\n")
            f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Daily backups section
            f.write("DAILY BACKUPS\n")
            f.write("-" * 50 + "\n")
            self.write_table_to_file(f, self.daily_table, "daily")
            
            # Weekly backups section
            f.write("\n\nWEEKLY BACKUPS\n")
            f.write("-" * 50 + "\n")
            self.write_table_to_file(f, self.weekly_table, "weekly")
            
            # Monthly backups section
            f.write("\n\nMONTHLY BACKUPS\n")
            f.write("-" * 50 + "\n")
            self.write_table_to_file(f, self.monthly_table, "monthly")
            
            # Summary section
            f.write("\n\nSUMMARY\n")
            f.write("-" * 50 + "\n")
            f.write(f"Total Daily Backups: {self.daily_table.rowCount()}\n")
            f.write(f"Total Weekly Backups: {self.weekly_table.rowCount()}\n")
            f.write(f"Total Monthly Backups: {self.monthly_table.rowCount()}\n")
            f.write(f"Grand Total: {self.daily_table.rowCount() + self.weekly_table.rowCount() + self.monthly_table.rowCount()}\n")

    def write_table_to_file(self, file_handle, table, table_type):
        """Write table data to file"""
        if table.rowCount() == 0:
            file_handle.write("No backups found.\n")
            return
        
        # Write header
        headers = []
        for col in range(table.columnCount()):
            headers.append(table.horizontalHeaderItem(col).text())
        
        file_handle.write(f"{'Sr No':<8} {'Date/Period':<20} {'Backup Name':<40} {'Type':<10}\n")
        file_handle.write("-" * 80 + "\n")
        
        # Write data rows
        for row in range(table.rowCount()):
            sr_no = table.item(row, 0).text() if table.item(row, 0) else ""
            date_period = table.item(row, 1).text() if table.item(row, 1) else ""
            backup_name = table.item(row, 2).text() if table.item(row, 2) else ""
            backup_type = table.item(row, 3).text() if table.item(row, 3) else ""
            
            file_handle.write(f"{sr_no:<8} {date_period:<20} {backup_name:<40} {backup_type:<10}\n")


def show_backup_report():
    """Standalone function to show backup report"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    window = CBackupReportWindow()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    show_backup_report()
